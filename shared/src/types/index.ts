// API Response Types
export type ApiResponse = {
  message: string;
  success: true;
}

export type ApiErrorResponse = {
  message: string;
  success: false;
  error?: string;
}

// Auth Types
export type Variables = {
  userId: string;
};

// Pagination Types
export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginationResponse {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// Transaction Types
export interface TransactionResponse {
  id: string;
  amount: number;
  type: string;
  date: Date;
}

export interface CreateTransactionRequest {
  amount: number;
  type: string;
}

// Payment Types
export interface PaymentResponse {
  id: string;
  amount: number;
  status: string;
  invoiceUrl: string;
  createdAt: Date;
}

export interface CreatePaymentRequest {
  amount: number;
  description?: string;
}

// Balance Types
export interface BalanceResponse {
  id: string;
  userBalance: number;
  createdAt: Date;
}

// User Types
export interface User {
  id: string;
  email: string;
  name?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserSession {
  id: string;
  userId: string;
  token: string;
  expiresAt: Date;
}

// Common Types
export type Status = 'pending' | 'completed' | 'failed' | 'cancelled';

export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}
