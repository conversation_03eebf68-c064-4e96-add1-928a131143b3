version: '3.8'

services:
  # Backend Service
  backend:
    image: your-dockerhub-username/sumopod-server:latest
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - BETTER_AUTH_SECRET=${BETTER_AUTH_SECRET}
      - BETTER_AUTH_URL=${BETTER_AUTH_URL}
      - BETTER_AUTH_TRUSTED_ORIGINS=${BETTER_AUTH_TRUSTED_ORIGINS}
      - XENDIT_API_KEY=${XENDIT_API_KEY}
      - XENDIT_API_URL=${XENDIT_API_URL}
      - XENDIT_CALLBACK_TOKEN=${XENDIT_CALLBACK_TOKEN}
      - PORT=8080
      - CORS_ORIGINS=${CORS_ORIGINS}
      - CORS_ALLOW_HEADERS=Content-Type,Authorization,X-Session-Token
      - CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
      - APP_NAME=sumopod-backend
      - EXTERNAL_ID_PREFIX=sumopod-
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Service
  frontend:
    image: your-dockerhub-username/sumopod-client:latest
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
