# Production Environment Variables
# Copy this file and update with your actual production values

# Database
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require

# Authentication
BETTER_AUTH_SECRET=9uEY2Wif0Es1Ud4ZGHBmONHjbE4bypDf
BETTER_AUTH_URL=https://your-backend-cloud-run-url
BETTER_AUTH_TRUSTED_ORIGINS=https://your-frontend-cloud-run-url

# Xendit Payment Gateway
XENDIT_API_KEY=xnd_development_aRnvtZu0b1pF6huwa1dyMeOgIqk2QELKZr1WTRzKo2zqKs2z1spb4bb3du0PeV
XENDIT_API_URL=https://api.xendit.co/v2/invoices
XENDIT_CALLBACK_TOKEN=sGqcEXjShdnLd4S6DITIbOxbbKWxsPK0018WQb8JqtoHxlo9

# CORS Configuration
CORS_ORIGINS=https://your-frontend-cloud-run-url
