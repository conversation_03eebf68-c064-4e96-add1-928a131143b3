{"name": "sumopod-monorepo", "version": "0.3.1", "description": "Sumopod monorepo built with Bun, Hono, Vite, and React", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "workspaces": ["./server", "./client", "./shared"], "scripts": {"dev:client": "cd client && bun run dev", "dev:server": "cd server && bun run dev", "dev:shared": "cd shared && bun run dev", "dev": "concurrently \"bun run dev:shared\" \"bun run dev:server\" \"bun run dev:client\"", "build:client": "cd client && bun run build", "build:shared": "cd shared && bun run build", "build:server": "cd server && bun run build", "build": "bun run build:shared && bun run build:server && bun run build:client", "test:client": "cd client && bun run test", "test": "bun run test:client", "lint:client": "cd client && bun run lint", "lint": "bun run lint:client", "docker:build": "docker-compose build", "docker:up": "docker-compose up", "docker:down": "docker-compose down", "docker:prod": "docker-compose -f docker-compose.prod.yml up", "postinstall": "bun run build:shared && bun run build:server"}, "keywords": ["bun", "hono", "react", "vite", "monorepo", "sumopod", "typescript", "prisma", "better-auth"], "devDependencies": {"bun-types": "latest", "concurrently": "^9.1.2"}, "peerDependencies": {"typescript": "^5.7.3"}, "dependencies": {"hono": "^4.8.4"}}