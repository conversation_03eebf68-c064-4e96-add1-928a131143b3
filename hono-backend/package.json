{"name": "hono-backend", "type": "module", "scripts": {"dev": "bun run --hot src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "dependencies": {"@hono/node-server": "^1.15.0", "@prisma/client": "^6.11.1", "@supabase/supabase-js": "^2.50.3", "better-auth": "^1.2.12", "dotenv": "^17.0.1", "hono": "^4.8.4"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^24.0.10", "prisma": "^6.11.1", "tsx": "^4.20.3", "typescript": "^5.8.3"}}